<?php
/**
 * 数据库配置文件
 * 支持自动检测MySQL端口(3306/3307)
 */

class DatabaseConfig {
    private static $instance = null;
    private $connection = null;
    
    // 数据库配置
    private $host = 'localhost';
    private $username = 'root';
    private $password = '';
    private $database = 'bitbear_system';
    private $charset = 'utf8mb4';
    private $ports = [3307, 3306]; // 支持的端口列表，优先使用3307
    
    private function __construct() {
        $this->connect();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function connect() {
        $connected = false;
        
        // 尝试不同端口连接
        foreach ($this->ports as $port) {
            try {
                $dsn = "mysql:host={$this->host};port={$port};charset={$this->charset}";
                $options = [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::ATTR_PERSISTENT => true, // 启用持久连接
                    PDO::ATTR_TIMEOUT => 5, // 设置连接超时
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset}",
                    PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true, // 使用缓冲查询
                    PDO::MYSQL_ATTR_FOUND_ROWS => true // 返回找到的行数而不是受影响的行数
                ];
                
                $this->connection = new PDO($dsn, $this->username, $this->password, $options);
                
                // 检查数据库是否存在，不存在则创建
                $this->createDatabaseIfNotExists();
                
                // 重新连接到指定数据库
                $dsn = "mysql:host={$this->host};port={$port};dbname={$this->database};charset={$this->charset}";
                $this->connection = new PDO($dsn, $this->username, $this->password, $options);
                
                $connected = true;
                error_log("数据库连接成功 - 端口: {$port}");
                break;
                
            } catch (PDOException $e) {
                error_log("端口 {$port} 连接失败: " . $e->getMessage());
                continue;
            }
        }
        
        if (!$connected) {
            throw new Exception('无法连接到MySQL数据库，请检查MySQL服务是否启动');
        }
    }
    
    private function createDatabaseIfNotExists() {
        try {
            $sql = "CREATE DATABASE IF NOT EXISTS `{$this->database}` CHARACTER SET {$this->charset} COLLATE {$this->charset}_unicode_ci";
            $this->connection->exec($sql);
        } catch (PDOException $e) {
            error_log("创建数据库失败: " . $e->getMessage());
        }
    }
    
    public function getConnection() {
        // 检查连接是否仍然有效
        if ($this->connection === null) {
            $this->connect();
        }
        
        try {
            $this->connection->query('SELECT 1');
        } catch (PDOException $e) {
            // 连接已断开，重新连接
            $this->connect();
        }
        
        return $this->connection;
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->getConnection()->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("数据库查询错误: " . $e->getMessage());
            throw $e;
        }
    }
    
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    public function execute($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    public function lastInsertId() {
        return $this->getConnection()->lastInsertId();
    }
    
    public function beginTransaction() {
        return $this->getConnection()->beginTransaction();
    }
    
    public function commit() {
        return $this->getConnection()->commit();
    }
    
    public function rollback() {
        return $this->getConnection()->rollback();
    }
    
    // 初始化数据库表结构
    public function initializeDatabase() {
        $sqlFile = __DIR__ . '/../database/init.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception('数据库初始化文件不存在');
        }
        
        $sql = file_get_contents($sqlFile);
        $statements = explode(';', $sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                try {
                    $this->getConnection()->exec($statement);
                } catch (PDOException $e) {
                    // 忽略已存在的表等错误
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        error_log("执行SQL语句失败: " . $e->getMessage());
                    }
                }
            }
        }
    }
}

// 便捷函数
function db() {
    return DatabaseConfig::getInstance();
}
?>
