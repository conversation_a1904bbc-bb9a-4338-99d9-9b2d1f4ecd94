/* 比特熊极简门户网站样式表 */

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8fafc;
}

/* 容器样式 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.center {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px; /* 固定导航栏高度 */
}

/* 头部导航样式 */
.header {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    height: 80px; /* 固定头部高度 */
}

.header img {
    height: 60px;
    width: 130px;
    object-fit: contain; /* 保持图片比例 */
}

/* 导航菜单样式 */
.list-left {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
    padding: 0;
    flex-direction: row;
    align-items: center;
    height: 100%; /* 占满父容器高度 */
}

.nav-item {
    position: relative;
    display: inline-block;
    height: 100%; /* 占满父容器高度 */
}

.nav-link {
    text-decoration: none;
    color: #374151;
    font-weight: 500;
    padding: 0.75rem 1rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: color 0.3s ease;
    white-space: nowrap;
    height: 100%; /* 占满父容器高度 */
    box-sizing: border-box;
}

.nav-link:hover {
    color: #3b82f6;
}

/* 下拉菜单样式 */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15), 0 5px 15px rgba(0, 0, 0, 0.08);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: 0.75rem 1rem;
    color: #374151;
    text-decoration: none;
    transition: background-color 0.2s ease;
}

.dropdown-item:hover {
    background-color: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.dropdown-arrow {
    transition: transform 0.3s ease;
}

.dropdown-arrow.rotated {
    transform: rotate(180deg);
}

/* 搜索功能样式 */
.search-container {
    position: relative;
    margin: 0 2rem;
    display: flex;
    align-items: center;
    height: 100%;
}

.search-form {
    position: relative;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 300px;
    padding: 0.75rem 1rem;
    padding-right: 3rem;
    border: 2px solid #e5e7eb;
    border-radius: 25px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background: white;
}

.search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-btn {
    position: absolute;
    right: 0.5rem;
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.search-btn:hover {
    color: #3b82f6;
    background-color: #f3f4f6;
}

/* 搜索建议样式 */
.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
    margin-top: 0.5rem;
}

.search-suggestions.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.suggestions-section {
    padding: 1rem;
}

.suggestions-section:not(:last-child) {
    border-bottom: 1px solid #f3f4f6;
}

.suggestions-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
}

.suggestion-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.suggestion-tag {
    background: #f3f4f6;
    color: #6b7280;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.suggestion-tag:hover {
    background: #3b82f6;
    color: white;
}

.recent-searches {
    list-style: none;
}

.recent-search-item {
    display: block;
    padding: 0.5rem 0;
    color: #6b7280;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
}

.recent-search-item:hover {
    color: #3b82f6;
}

/* 右侧导航样式 */
.list-right {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 0.75rem;
    margin: 0;
    padding: 0;
    flex-direction: row;
    height: 100%; /* 占满父容器高度 */
}

.loading a, .zhuche a {
    text-decoration: none;
    padding: 0.75rem 1rem;
    border-radius: 4px;
    font-weight: 400;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    border: 1px solid transparent;
    white-space: nowrap;
    text-align: center;
    height: 40px;
    box-sizing: border-box;
}

.loading a {
    background: white;
    color: #333;
    border: 1px solid #d1d5db;
}

.loading a:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.loading a:active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
    transform: translateY(1px);
}

.zhuche a {
    background: #1d4ed8;
    color: white;
    border: 1px solid #1d4ed8;
}

.zhuche a:hover {
    background: #1e40af;
    border-color: #1e40af;
    box-shadow: 0 2px 8px rgba(29, 78, 216, 0.3);
}

.admin-link a {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.4rem;
    text-decoration: none;
    color: #6b7280;
    font-size: 0.85rem;
    transition: color 0.3s ease;
    padding: 0.75rem 0.8rem;
    border-radius: 4px;
    height: 40px;
    box-sizing: border-box;
    white-space: nowrap;
}

.admin-link a:hover {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

/* 移动端样式 */
.mobile-search-btn-container,
.mobile-menu-btn {
    display: none;
}

/* 主要内容区域 */
.main-content {
    min-height: calc(100vh - 80px);
}

/* 英雄区域样式 - 模仿参考图片的渐变背景 */
.hero-section {
    background: linear-gradient(-70deg, rgb(20, 212, 216) 0%, rgb(0, 113, 235) 60%, rgb(0, 113, 235) 80%, rgb(142, 34, 167) 100%);
    min-height: 85vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    width: 100%;
}

.hero-text {
    color: white;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.5rem;
    font-weight: 400;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.hero-description {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
    opacity: 0.8;
}

.hero-actions {
    display: flex;
    gap: 1rem;
}

.btn {
    display: inline-block;
    padding: 0.875rem 2rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-primary {
    background: white;
    color: #3b82f6;
    font-size: 1rem;
    padding: 1rem 2.5rem;
}

.btn-primary:hover {
    background: #f8fafc;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    background: transparent;
    color: white;
    border-color: white;
    font-size: 1rem;
    padding: 1rem 2.5rem;
}

.btn-secondary:hover {
    background: white;
    color: #3b82f6;
}

/* 英雄区域图片 - 模仿参考图片的红色圆形logo */
.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-img {
    width: 180px;
    height: 180px;
    border-radius: 50%;
    background: white;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: float 3s ease-in-out infinite;
    border: 4px solid rgba(255, 255, 255, 0.3);
    object-fit: cover;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* 特色功能区域样式 */
.features-section {
    padding: 5rem 0;
    background: white;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #f3f4f6;
    transition: all 0.3s ease;
    text-align: center;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.feature-description {
    color: #6b7280;
    line-height: 1.6;
}

/* 统计数据区域样式 - O'Reilly风格美化 */
.stats-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
    padding: 6rem 0;
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(37, 99, 235, 0.06) 0%, transparent 50%);
    pointer-events: none;
}

.stats-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%233b82f6" fill-opacity="0.03"><circle cx="20" cy="20" r="1"/></g></g></svg>');
    pointer-events: none;
}

/* 统计区域标题样式 */
.stats-header {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
    z-index: 1;
}

.stats-title {
    font-size: 3.2rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #1e293b 0%, #475569 50%, #64748b 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.1;
    letter-spacing: -0.02em;
    position: relative;
}

.stats-title::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
    border-radius: 2px;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

.stats-subtitle {
    font-size: 1.3rem;
    color: #64748b;
    font-weight: 500;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 3rem;
    position: relative;
    z-index: 1;
}

.stat-item {
    text-align: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    border-radius: 20px;
    padding: 3rem 2rem;
    backdrop-filter: blur(20px);
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9),
        0 0 0 1px rgba(59, 130, 246, 0.1);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.6s;
}

.stat-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 30px 80px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 1),
        0 0 0 2px rgba(59, 130, 246, 0.2);
}

.stat-item:hover::before {
    left: 100%;
}

/* 统计项图标样式 */
.stat-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 2rem;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.05) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #3b82f6;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(37, 99, 235, 0.1) 100%);
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.stat-item:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
}

.stat-item:hover .stat-icon::before {
    opacity: 1;
}

.stat-icon svg {
    width: 48px;
    height: 48px;
    position: relative;
    z-index: 1;
}

.stat-number {
    font-size: 3.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    display: block;
    line-height: 1.1;
    text-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    position: relative;
}

.stat-number::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
    border-radius: 2px;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

.stat-label {
    font-size: 1.2rem;
    color: #475569;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-top: 1rem;
}

/* 页脚样式 */
.footer {
    background: #1f2937;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-brand {
    max-width: 400px;
}

.footer-logo {
    height: 50px;
    width: auto;
    margin-bottom: 1rem;
}

.footer-description {
    color: #d1d5db;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: #374151;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #d1d5db;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #3b82f6;
    color: white;
    transform: translateY(-2px);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.link-group-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
}

.link-list {
    list-style: none;
}

.link-list li {
    margin-bottom: 0.5rem;
}

.footer-link {
    color: #d1d5db;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: #3b82f6;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 1rem;
    text-align: center;
}

.copyright {
    color: #9ca3af;
    font-size: 0.9rem;
}

/* 移动端搜索模态框样式 */
.mobile-search-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.mobile-search-modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    position: relative;
    background: white;
    margin: 2rem;
    border-radius: 12px;
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid #f3f4f6;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.modal-close-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.modal-close-btn:hover {
    background: #f3f4f6;
    color: #1f2937;
}

.modal-body {
    padding: 1.5rem;
}

.mobile-search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
}

.mobile-search-input {
    width: 100%;
    padding: 1rem;
    padding-right: 3rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.mobile-search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.mobile-search-submit-btn {
    position: absolute;
    right: 0.5rem;
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.mobile-search-submit-btn:hover {
    color: #3b82f6;
    background-color: #f3f4f6;
}

/* 移动端搜索建议样式 */
.mobile-search-suggestions {
    margin-top: 1rem;
}

.mobile-suggestions-section {
    margin-bottom: 2rem;
}

.mobile-suggestions-title {
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 1rem;
}

.mobile-suggestion-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.mobile-suggestion-tag {
    background: #f3f4f6;
    color: #6b7280;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mobile-suggestion-tag:hover {
    background: #3b82f6;
    color: white;
}

.mobile-recent-searches {
    list-style: none;
}

.mobile-recent-search-item {
    display: block;
    padding: 0.75rem 0;
    color: #6b7280;
    text-decoration: none;
    font-size: 1rem;
    border-bottom: 1px solid #f3f4f6;
    transition: color 0.2s ease;
}

.mobile-recent-search-item:hover {
    color: #3b82f6;
}

/* 移动端菜单按钮样式 */
.mobile-menu-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.hamburger-line {
    width: 24px;
    height: 2px;
    background: #374151;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.mobile-menu-btn.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-btn.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-btn.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .features-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-links {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .center {
        padding: 0 1rem;
    }

    .list-left {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        flex-direction: column;
        gap: 0;
        margin: 1rem;
        padding: 1rem 0;
    }

    .list-left.active {
        display: flex;
    }

    .nav-item {
        width: 100%;
    }

    .nav-link {
        padding: 1rem;
        border-bottom: 1px solid #f3f4f6;
    }

    .dropdown-menu {
        position: static;
        opacity: 0;
        visibility: hidden;
        max-height: 0;
        overflow: hidden;
        transform: none;
        box-shadow: none;
        border: none;
        border-radius: 8px;
        background: rgba(249, 250, 251, 0.9);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        margin-left: 1rem;
        transition: all 0.3s ease;
    }

    .dropdown-menu.show {
        opacity: 1;
        visibility: visible;
        max-height: 300px;
    }

    .dropdown-item {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid rgba(229, 231, 235, 0.5);
    }

    .dropdown-item:last-child {
        border-bottom: none;
    }

    .search-container {
        display: none;
    }

    .mobile-search-btn-container {
        display: block;
    }

    .mobile-menu-btn {
        display: flex;
    }

    .list-right {
        gap: 0.5rem;
    }

    .loading a, .zhuche a {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }

    .admin-link {
        display: none;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.25rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
        text-align: center;
    }

    .hero-img {
        width: 150px;
        height: 150px;
    }

    .section-title {
        font-size: 2rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .stats-section {
        padding: 4rem 0;
    }

    .stats-header {
        margin-bottom: 3rem;
    }

    .stats-title {
        font-size: 2.6rem;
    }

    .stats-title::after {
        width: 80px;
        background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
    }

    .stats-subtitle {
        font-size: 1.1rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    .stat-item {
        padding: 2.5rem 1.5rem;
    }

    .stat-icon {
        width: 70px;
        height: 70px;
        margin-bottom: 1.5rem;
    }

    .stat-icon svg {
        width: 40px;
        height: 40px;
    }

    .stat-number {
        font-size: 2.8rem;
    }

    .stat-label {
        font-size: 1.1rem;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

@media (max-width: 480px) {
    .container, .center {
        padding: 0 0.75rem;
    }

    .hero-title {
        font-size: 1.75rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .stats-section {
        padding: 3rem 0;
    }

    .stats-header {
        margin-bottom: 2.5rem;
    }

    .stats-title {
        font-size: 2.2rem;
    }

    .stats-title::after {
        width: 60px;
        background: linear-gradient(90deg, #3b82f6 0%, #2563eb 100%);
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
    }

    .stats-subtitle {
        font-size: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .stat-item {
        padding: 2rem 1.5rem;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 1rem;
    }

    .stat-icon svg {
        width: 32px;
        height: 32px;
    }

    .stat-number {
        font-size: 2.2rem;
    }

    .stat-label {
        font-size: 1rem;
        letter-spacing: 0.5px;
    }

    .modal-content {
        margin: 1rem;
        border-radius: 8px;
    }
}

/* 统计数据动画效果 */
.stat-updating {
    animation: stat-pulse 0.6s ease-in-out;
}

.stat-updated {
    animation: stat-highlight 1s ease-in-out;
}

@keyframes stat-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes stat-highlight {
    0% {
        background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 50%, #1d4ed8 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    50% {
        background: linear-gradient(135deg, #60a5fa 0%, #93c5fd 50%, #3b82f6 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
    }
    100% {
        background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 50%, #1d4ed8 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
}

/* 统计项入场动画 */
.stat-item {
    animation: stat-fade-in 0.8s ease-out forwards;
    opacity: 0;
    transform: translateY(30px);
}

.stat-item:nth-child(1) { animation-delay: 0.1s; }
.stat-item:nth-child(2) { animation-delay: 0.2s; }
.stat-item:nth-child(3) { animation-delay: 0.3s; }
.stat-item:nth-child(4) { animation-delay: 0.4s; }

@keyframes stat-fade-in {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 动画和过渡效果 */
.stat-updating {
    opacity: 0.5;
    transform: scale(0.95);
    transition: all 0.3s ease;
}

.stat-updated {
    opacity: 1;
    transform: scale(1.05);
    transition: all 0.3s ease;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 无障碍和可用性改进 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .btn-primary {
        border: 2px solid #000;
    }

    .btn-secondary {
        border: 2px solid #fff;
    }

    .feature-card {
        border: 2px solid #000;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #0f172a;
        color: #e2e8f0;
    }

    .header {
        background: rgba(15, 23, 42, 0.95);
        border-bottom-color: rgba(255, 255, 255, 0.1);
    }

    .nav-link {
        color: #e2e8f0;
    }

    .nav-link:hover {
        color: #60a5fa;
    }

    .dropdown-menu {
        background: rgba(30, 41, 59, 0.85);
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        border-color: rgba(51, 65, 85, 0.5);
    }

    .dropdown-item {
        color: #e2e8f0;
    }

    .dropdown-item:hover {
        background-color: rgba(96, 165, 250, 0.15);
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        color: #60a5fa;
    }

    .search-input {
        background: #1e293b;
        border-color: #334155;
        color: #e2e8f0;
    }

    .search-suggestions {
        background: #1e293b;
        border-color: #334155;
    }

    .features-section {
        background: #0f172a;
    }

    .feature-card {
        background: #1e293b;
        border-color: #334155;
    }

    .modal-content {
        background: #1e293b;
    }

    .mobile-search-input {
        background: #334155;
        border-color: #475569;
        color: #e2e8f0;
    }
}

/* O'Reilly风格技能建设区域样式 - 按照官网风格美化 */
.oreilly-hero-section {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 30%, #0f3460 70%, #533483 100%);
    padding: 120px 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.oreilly-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(255, 87, 51, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.12) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(147, 51, 234, 0.06) 0%, transparent 70%);
    pointer-events: none;
}

.oreilly-hero-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.02"><circle cx="30" cy="30" r="1"/></g></g></svg>');
    pointer-events: none;
}

/* 主要内容布局 */
.hero-main-content {
    display: flex;
    align-items: center;
    gap: 80px;
    margin-bottom: 80px;
    position: relative;
    z-index: 1;
}

/* 左侧视觉区域 */
.hero-visual-area {
    position: relative;
    flex-shrink: 0;
}

.large-circle {
    width: 320px;
    height: 320px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(25px);
    border: 3px solid rgba(255, 255, 255, 0.25);
    position: relative;
    animation: gentle-float 10s ease-in-out infinite;
    box-shadow:
        0 25px 80px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 0 0 1px rgba(255, 87, 51, 0.1);
}

.butterfly-svg {
    width: 160px;
    height: 160px;
    animation: subtle-flutter 8s ease-in-out infinite;
    filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.2));
}

.white-dot {
    position: absolute;
    top: 45px;
    right: 45px;
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
    border-radius: 50%;
    animation: gentle-pulse 5s ease-in-out infinite;
    box-shadow:
        0 10px 30px rgba(255, 255, 255, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        0 0 0 2px rgba(255, 87, 51, 0.15);
}

/* 动画效果 - O'Reilly风格优化 */
@keyframes gentle-float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
        box-shadow:
            0 25px 80px rgba(0, 0, 0, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            0 0 0 1px rgba(255, 87, 51, 0.1);
    }
    50% {
        transform: translateY(-25px) rotate(3deg) scale(1.02);
        box-shadow:
            0 35px 100px rgba(0, 0, 0, 0.5),
            inset 0 1px 0 rgba(255, 255, 255, 0.4),
            0 0 0 1px rgba(255, 87, 51, 0.2);
    }
}

@keyframes subtle-flutter {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.2));
    }
    50% {
        transform: scale(1.08) rotate(2deg);
        filter: drop-shadow(0 6px 18px rgba(0, 0, 0, 0.3));
    }
}

@keyframes gentle-pulse {
    0%, 100% {
        opacity: 0.95;
        transform: scale(1);
        box-shadow:
            0 10px 30px rgba(255, 255, 255, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.8),
            0 0 0 2px rgba(255, 87, 51, 0.15);
    }
    50% {
        opacity: 1;
        transform: scale(1.15);
        box-shadow:
            0 15px 40px rgba(255, 255, 255, 0.6),
            inset 0 1px 0 rgba(255, 255, 255, 1),
            0 0 0 2px rgba(255, 87, 51, 0.25);
    }
}

/* 右侧文本内容 */
.hero-text-content {
    flex: 1;
    max-width: 600px;
}

.hero-main-title {
    font-size: 4.2rem;
    font-weight: 800;
    margin-bottom: 2.5rem;
    line-height: 1.05;
    letter-spacing: -0.03em;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    position: relative;
}

.hero-main-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 120px;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
    border-radius: 2px;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

.hero-description {
    font-size: 1.35rem;
    margin-bottom: 3.5rem;
    line-height: 1.65;
    opacity: 0.92;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.95);
}

.hero-description strong {
    font-weight: 700;
    color: #93c5fd;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 按钮样式 - O'Reilly风格优化 */
.hero-action-buttons {
    display: flex;
    gap: 2.5rem;
    margin-top: 1rem;
}

.btn-request-demo, .btn-try-free {
    padding: 18px 36px;
    font-size: 1.15rem;
    font-weight: 700;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: 10px;
    position: relative;
    overflow: hidden;
    text-transform: none;
    letter-spacing: 0.5px;
}

.btn-request-demo {
    background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
    color: white;
    border: 2px solid transparent;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.btn-request-demo::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.btn-request-demo:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #3b82f6 100%);
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 40px rgba(59, 130, 246, 0.5);
}

.btn-request-demo:hover::before {
    left: 100%;
}

.btn-try-free {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn-try-free:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: white;
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 40px rgba(255, 255, 255, 0.2);
}

/* 功能导航栏 - O'Reilly风格优化 */
.features-navigation-bar {
    display: flex;
    justify-content: center;
    gap: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.25) 100%);
    border-radius: 25px;
    padding: 16px;
    backdrop-filter: blur(40px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    max-width: 1300px;
    margin: 0 auto;
    overflow-x: auto;
    position: relative;
    z-index: 1;
    box-shadow:
        0 15px 50px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        0 0 0 1px rgba(255, 87, 51, 0.1);
}

.feature-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 24px 18px;
    min-width: 130px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 18px;
    position: relative;
    background: rgba(255, 255, 255, 0.02);
}

.feature-nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 18px;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.feature-nav-item:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.feature-nav-item:hover::before {
    opacity: 1;
}

.feature-nav-item.active {
    background: linear-gradient(135deg, rgba(255, 87, 51, 0.2) 0%, rgba(255, 140, 66, 0.15) 100%);
    border: 2px solid rgba(255, 87, 51, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(255, 87, 51, 0.3);
}

.feature-nav-item.active::after {
    content: '';
    position: absolute;
    bottom: -16px;
    left: 50%;
    transform: translateX(-50%);
    width: 10px;
    height: 10px;
    background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
    border-radius: 50%;
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.8);
}

.feature-nav-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 14px;
    color: rgba(255, 255, 255, 0.85);
    transition: all 0.4s ease;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.feature-nav-item:hover .feature-nav-icon {
    color: rgba(255, 255, 255, 0.95);
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}

.feature-nav-item.active .feature-nav-icon {
    color: #ffffff;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(96, 165, 250, 0.6) 100%);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
    transform: scale(1.05);
}

.feature-nav-label {
    font-size: 0.9rem;
    font-weight: 600;
    text-align: center;
    line-height: 1.4;
    opacity: 0.88;
    transition: all 0.4s ease;
    color: rgba(255, 255, 255, 0.9);
}

.feature-nav-item:hover .feature-nav-label {
    opacity: 1;
    color: rgba(255, 255, 255, 1);
}

.feature-nav-item.active .feature-nav-label {
    opacity: 1;
    color: #ffffff;
    font-weight: 700;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* O'Reilly风格专家课程区域样式 */
.expert-courses-section {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 30%, #0f3460 70%, #533483 100%);
    padding: 100px 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.expert-courses-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.12) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(147, 51, 234, 0.06) 0%, transparent 70%);
    pointer-events: none;
}

.courses-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6rem;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.course-item {
    text-align: center;
    padding: 0 2rem;
}

.course-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    line-height: 1.2;
    color: #ffffff;
    letter-spacing: -0.02em;
}

.course-description {
    font-size: 1.1rem;
    margin-bottom: 2.5rem;
    color: rgba(255, 255, 255, 0.85);
    line-height: 1.6;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.course-btn {
    display: inline-block;
    padding: 14px 28px;
    font-weight: 600;
    font-size: 1rem;
    background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.course-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.course-btn:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #3b82f6 100%);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    color: white;
    text-decoration: none;
}

.course-btn:hover::before {
    left: 100%;
}

/* O'Reilly风格专家展示区域样式 */
.experts-section {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 30%, #0f3460 70%, #533483 100%);
    padding: 120px 0;
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.experts-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(147, 51, 234, 0.08) 0%, transparent 50%);
    pointer-events: none;
}

.experts-header {
    max-width: 800px;
    margin: 0 auto 80px;
    position: relative;
    z-index: 1;
}

.experts-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 2rem;
    line-height: 1.2;
    color: #ffffff;
    letter-spacing: -0.02em;
}

.experts-description {
    font-size: 1.1rem;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.85);
    max-width: 700px;
    margin: 0 auto;
}

.experts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.expert-card {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.expert-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.expert-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.12);
}

.expert-card:hover::before {
    opacity: 1;
}

.expert-image-container {
    position: relative;
    margin-bottom: 1rem;
    border-radius: 12px;
    overflow: hidden;
}

.expert-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.expert-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(147, 51, 234, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.expert-card:hover .expert-image {
    transform: scale(1.05);
}

.expert-card:hover .expert-overlay {
    opacity: 1;
}

.expert-info {
    position: relative;
    z-index: 2;
}

.expert-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #ffffff;
}

.expert-title {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
    line-height: 1.4;
}




}

/* 紧凑型推荐视频区域样式 */
.compact-testimonial-section {
    background:
        url('image/raven_blob_2.jpg') left top/30% no-repeat,
        url('image/raven_blob_3.jpg') right bottom/25% no-repeat,
        linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 60px 0;
    color: #1e293b;
    position: relative;
    overflow: hidden;
}

.compact-testimonial-content {
    display: grid;
    grid-template-columns: 1fr 1.2fr;
    gap: 3rem;
    align-items: center;
    max-width: 1000px;
    margin: 0 auto;
}

/* 左侧视频区域 */
.compact-video-container {
    position: relative;
}

.compact-video-wrapper {
    position: relative;
    width: 100%;
    aspect-ratio: 16/9;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* 为不支持backdrop-filter的浏览器提供备用方案 */
@supports not (backdrop-filter: blur(20px)) {
    .compact-video-overlay {
        background: rgba(0, 0, 0, 0.7) !important;
    }

    .compact-video-wrapper:hover .compact-video-overlay {
        background: rgba(0, 0, 0, 0.8) !important;
    }
}

.compact-video-wrapper:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.3);
}

.compact-play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
    z-index: 3;
}

.compact-play-button:hover {
    transform: translate(-50%, -50%) scale(1.1);
}

.compact-video-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1.2rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.compact-video-wrapper:hover .compact-video-overlay {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
}

.compact-speaker-info {
    text-align: left;
    flex: 1;
}

.compact-speaker-name {
    display: block;
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.3rem;
    color: white;
    text-shadow: 0 1px 3px rgba(0,0,0,0.3);
    letter-spacing: -0.01em;
}

.compact-speaker-role {
    display: block;
    font-size: 0.85rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.compact-company-badge {
    background: #ff6b35;
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 700;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.compact-video-wrapper:hover .compact-company-badge {
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.4);
    transform: translateY(-1px);
}

/* 视频导航点 */
.compact-video-dots {
    display: flex;
    justify-content: center;
    gap: 0.4rem;
    margin-top: 1rem;
}

.compact-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(30, 41, 59, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.compact-dot.active,
.compact-dot:hover {
    background: #667eea;
    transform: scale(1.3);
}

/* 右侧详情信息 */
.compact-testimonial-details {
    padding-left: 1rem;
}

.compact-testimonial-header {
    margin-bottom: 1rem;
}

.compact-rating {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(251, 191, 36, 0.1);
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    border: 1px solid rgba(251, 191, 36, 0.2);
}

.compact-rating-text {
    font-size: 0.8rem;
    font-weight: 600;
    color: #d97706;
}

.compact-testimonial-title {
    font-size: 1.8rem;
    font-weight: 700;
    line-height: 1.3;
    margin: 0 0 1rem 0;
    color: #1e293b;
    letter-spacing: -0.01em;
}

.compact-testimonial-description {
    font-size: 1rem;
    line-height: 1.6;
    margin: 0 0 1.5rem 0;
    color: #64748b;
    font-style: italic;
}

.compact-testimonial-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 1.5rem;
    padding: 1rem 0;
    border-top: 1px solid rgba(30, 41, 59, 0.1);
    border-bottom: 1px solid rgba(30, 41, 59, 0.1);
}

.compact-stat {
    text-align: center;
}

.compact-stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.2rem;
}

.compact-stat-label {
    display: block;
    font-size: 0.8rem;
    color: #64748b;
    font-weight: 500;
}

.compact-testimonial-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    background: #667eea;
    color: white;
    border: 2px solid #667eea;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.compact-testimonial-btn:hover {
    background: #5a67d8;
    border-color: #5a67d8;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.compact-testimonial-btn svg {
    transition: transform 0.3s ease;
}

.compact-testimonial-btn:hover svg {
    transform: translateX(2px);
}

/* O'Reilly风格底部行动号召区域样式 */
.oreilly-cta-section {
    background: linear-gradient(242deg, rgb(142, 34, 167) 0.01%, rgb(37, 99, 234) 97.15%);
    padding: 60px 0;
    text-align: center;
    color: white;
    position: relative;
    overflow: hidden;
}

.oreilly-cta-content {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.oreilly-cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 2.5rem;
    line-height: 1.2;
    color: white;
    letter-spacing: -0.02em;
}

.oreilly-cta-actions {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
    align-items: center;
}

.oreilly-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 14px 28px;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.oreilly-btn-primary {
    background: rgb(0, 113, 235);
    color: white;
    border-color: rgb(0, 113, 235);
}

.oreilly-btn-primary:hover {
    background: rgb(0, 95, 200);
    border-color: rgb(0, 95, 200);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 113, 235, 0.3);
}

.oreilly-btn-secondary {
    background: transparent;
    color: white;
    border-color: rgba(255, 255, 255, 0.6);
}

.oreilly-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.oreilly-btn svg {
    transition: transform 0.3s ease;
}

.oreilly-btn:hover svg {
    transform: translateX(3px);
}

/* O'Reilly风格区域的响应式设计 - 美化版本 */
@media (max-width: 768px) {
    .oreilly-hero-section {
        padding: 80px 0;
    }

    .hero-main-content {
        flex-direction: column;
        text-align: center;
        gap: 4.5rem;
        margin-bottom: 4.5rem;
    }

    .large-circle {
        width: 260px;
        height: 260px;
    }

    .butterfly-svg {
        width: 130px;
        height: 130px;
    }

    .white-dot {
        width: 55px;
        height: 55px;
        top: 35px;
        right: 35px;
    }

    .hero-main-title {
        font-size: 3.2rem;
    }

    .hero-main-title::after {
        width: 100px;
        left: 50%;
        transform: translateX(-50%);
    }

    .hero-description {
        font-size: 1.2rem;
    }

    .hero-action-buttons {
        justify-content: center;
        flex-wrap: wrap;
        gap: 2rem;
    }

    .btn-request-demo, .btn-try-free {
        padding: 16px 30px;
        font-size: 1.05rem;
    }

    .features-navigation-bar {
        flex-wrap: wrap;
        gap: 10px;
        padding: 20px;
        border-radius: 20px;
        max-width: 100%;
    }

    .feature-nav-item {
        min-width: 110px;
        padding: 20px 14px;
    }

    .feature-nav-icon {
        width: 36px;
        height: 36px;
    }

    .feature-nav-label {
        font-size: 0.85rem;
    }


}

@media (max-width: 480px) {
    .oreilly-hero-section {
        padding: 60px 0;
    }

    .hero-main-content {
        gap: 3.5rem;
        margin-bottom: 3.5rem;
    }

    .large-circle {
        width: 220px;
        height: 220px;
    }

    .butterfly-svg {
        width: 110px;
        height: 110px;
    }

    .white-dot {
        width: 45px;
        height: 45px;
        top: 25px;
        right: 25px;
    }

    .hero-main-title {
        font-size: 2.6rem;
    }

    .hero-main-title::after {
        width: 80px;
    }

    .hero-description {
        font-size: 1.1rem;
    }

    .hero-action-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
    }

    .btn-request-demo, .btn-try-free {
        width: 100%;
        max-width: 300px;
        justify-content: center;
        padding: 16px 24px;
    }

    .features-navigation-bar {
        padding: 16px;
        gap: 8px;
    }

    .feature-nav-item {
        min-width: 95px;
        padding: 16px 10px;
    }

    .feature-nav-icon {
        width: 32px;
        height: 32px;
        margin-bottom: 10px;
    }

    .feature-nav-label {
        font-size: 0.8rem;
        line-height: 1.3;
    }


}

    /* O'Reilly风格专家课程区域响应式 */
    .expert-courses-section {
        padding: 80px 0;
    }

    .courses-grid {
        grid-template-columns: 1fr;
        gap: 4rem;
    }

    .course-item {
        padding: 0 1rem;
    }

    .course-title {
        font-size: 2rem;
        margin-bottom: 1.5rem;
    }

    .course-description {
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    .course-btn {
        padding: 12px 24px;
        font-size: 0.95rem;
    }

    .course-description {
        font-size: 1rem;
    }

    /* 专家展示区域响应式 */
    .experts-section {
        padding: 80px 0;
    }

    .experts-title {
        font-size: 2.2rem;
    }

    .experts-grid {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 1.5rem;
    }

    .expert-card {
        padding: 1.2rem;
    }

    .expert-image {
        height: 100px;
    }

    /* 组织领导展示区域响应式 */
    .leadership-title {
        font-size: 2rem;
    }

    .leadership-description {
        font-size: 1rem;
    }

    .leaders-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .leader-card {
        padding: 1.5rem 1rem;
    }

    .leader-avatar {
        width: 60px;
        height: 60px;
    }

    /* 紧凑型推荐视频区域响应式 */
    .compact-testimonial-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .compact-testimonial-details {
        padding-left: 0;
        order: -1;
    }

    .compact-testimonial-stats {
        justify-content: center;
        gap: 1.5rem;
    }

    .compact-testimonial-title {
        font-size: 1.5rem;
    }

    .compact-testimonial-description {
        font-size: 0.95rem;
    }

    /* O'Reilly风格底部行动号召区域响应式 */
    .oreilly-cta-title {
        font-size: 2rem;
    }

    .oreilly-cta-actions {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .oreilly-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .features-icons-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .experts-section {
        padding: 60px 0;
    }

    .experts-title {
        font-size: 1.8rem;
        line-height: 1.3;
    }

    .experts-description {
        font-size: 1rem;
    }

    .experts-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .expert-card {
        padding: 1rem;
    }

    .expert-image {
        height: 80px;
    }

    .expert-name {
        font-size: 1rem;
    }

    .expert-title {
        font-size: 0.85rem;
    }

    .leaders-grid {
        grid-template-columns: 1fr;
    }

    /* 专家课程区域小屏幕响应式 */
    .expert-courses-section {
        padding: 60px 0;
    }

    .courses-grid {
        gap: 3rem;
    }

    .course-title {
        font-size: 1.8rem;
        line-height: 1.3;
    }

    .course-description {
        font-size: 0.95rem;
    }

    .course-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .skills-title {
        font-size: 1.8rem;
    }

    .leadership-title {
        font-size: 1.8rem;
    }

    .compact-testimonial-content {
        gap: 1.5rem;
    }

    .compact-testimonial-stats {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .compact-testimonial-title {
        font-size: 1.3rem;
    }

    .compact-testimonial-description {
        font-size: 0.9rem;
    }

    .compact-testimonial-btn {
        padding: 10px 18px;
        font-size: 0.85rem;
    }

    .compact-stat-number {
        font-size: 1.2rem;
    }

    .oreilly-cta-title {
        font-size: 1.6rem;
    }

    .oreilly-btn {
        padding: 12px 24px;
        font-size: 0.95rem;
    }
}
