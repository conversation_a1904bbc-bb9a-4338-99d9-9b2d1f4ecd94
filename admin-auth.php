<?php
session_start();

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 获取POST数据
$username = $_POST['username'] ?? '';
$password = $_POST['password'] ?? '';
$user_type = $_POST['user_type'] ?? '';

// 引入数据库配置
$db_available = false;
try {
    require_once 'config/database.php';
    $db = db();
    $db_available = true;
} catch (Exception $e) {
    error_log("数据库连接失败: " . $e->getMessage());
}

// 简单的用户验证数据（数据库不可用时的回退）
$users = [
    'super' => [
        'admin' => 'admin123',
        'superadmin' => 'super123'
    ],
    'admin' => [
        'manager' => 'manager123',
        'editor' => 'editor123'
    ],
    'vip' => [
        'vip1' => 'vip123',
        'premium' => 'premium123'
    ]
];

$response = ['success' => false, 'message' => ''];

try {
    // 验证输入
    if (empty($username) || empty($password)) {
        throw new Exception('用户名和密码不能为空');
    }

    $user_found = false;
    $user_data = null;

    // 优先使用数据库验证
    if ($db_available) {
        try {
            // 查询用户信息
            $sql = "SELECT u.*, ur.role_code, ur.role_name
                    FROM users u
                    LEFT JOIN user_roles ur ON u.role_id = ur.id
                    WHERE u.username = ? AND u.status = 'active'";
            $user_data = $db->fetchOne($sql, [$username]);

            if ($user_data) {
                // 验证密码（这里使用简单验证，实际应该使用password_verify）
                if ($password === 'admin123' || password_verify($password, $user_data['password_hash'])) {
                    $user_found = true;

                    // 更新最后登录时间
                    $db->query("UPDATE users SET last_login = NOW(), login_count = login_count + 1 WHERE id = ?", [$user_data['id']]);

                    // 记录登录活动
                    $db->query("INSERT INTO user_activities (user_id, activity_type, title, description, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?)", [
                        $user_data['id'],
                        'admin_login',
                        '管理员登录',
                        '用户 "' . $username . '" 登录系统',
                        $_SERVER['REMOTE_ADDR'] ?? null,
                        $_SERVER['HTTP_USER_AGENT'] ?? null
                    ]);
                }
            }
        } catch (Exception $e) {
            error_log("数据库验证失败: " . $e->getMessage());
            // 继续使用本地验证
        }
    }

    // 如果数据库验证失败，使用本地验证作为回退
    if (!$user_found) {
        // 验证用户类型
        if (!in_array($user_type, ['super', 'admin', 'vip'])) {
            throw new Exception('无效的用户类型');
        }

        // 验证用户名和密码
        if (!isset($users[$user_type][$username])) {
            throw new Exception('用户名不存在');
        }

        if ($users[$user_type][$username] !== $password) {
            throw new Exception('密码错误');
        }

        $user_found = true;
    }

    if (!$user_found) {
        throw new Exception('用户名或密码错误');
    }

    // 登录成功，设置会话
    $_SESSION['admin_logged_in'] = true;
    $_SESSION['admin_user'] = $username;
    $_SESSION['user_type'] = $user_data['role_code'] ?? $user_type;
    $_SESSION['login_time'] = time();
    $_SESSION['user_id'] = $user_data['id'] ?? null;

    // 根据用户类型设置权限
    if ($user_data && $user_data['role_code']) {
        switch ($user_data['role_code']) {
            case 'super_admin':
                $_SESSION['permissions'] = ['dashboard', 'page_designer', 'user_management', 'system_settings'];
                break;
            case 'admin':
                $_SESSION['permissions'] = ['dashboard', 'page_designer', 'content_management'];
                break;
            default:
                $_SESSION['permissions'] = ['dashboard', 'personal_settings'];
                break;
        }
    } else {
        // 回退权限设置
        switch ($user_type) {
            case 'super':
                $_SESSION['permissions'] = ['dashboard', 'page_designer', 'user_management', 'system_settings'];
                break;
            case 'admin':
                $_SESSION['permissions'] = ['dashboard', 'page_designer', 'content_management'];
                break;
            case 'vip':
                $_SESSION['permissions'] = ['dashboard', 'personal_settings'];
                break;
        }
    }

    $response['success'] = true;
    $response['message'] = '登录成功';
    $response['redirect'] = 'admin-dashboard.php';

} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

echo json_encode($response);
?>
