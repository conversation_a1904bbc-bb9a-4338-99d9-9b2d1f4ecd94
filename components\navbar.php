<?php
/**
 * 前台导航栏组件
 * 从数据库动态加载导航栏结构
 */

require_once __DIR__ . '/../config/database.php';

function getNavbarData() {
    // 使用缓存来提高性能
    static $cachedNavbar = null;
    static $cacheTime = null;

    // 缓存5分钟
    if ($cachedNavbar !== null && $cacheTime !== null && (time() - $cacheTime) < 300) {
        return $cachedNavbar;
    }

    try {
        $db = db();

        // 优化的SQL查询，只获取必要字段
        $sql = "SELECT id, name, url, type, parent_id, sort_order FROM navbar_items WHERE visible = 1 ORDER BY sort_order ASC, id ASC";
        $items = $db->fetchAll($sql);

        // 构建树形结构
        $result = buildNavbarTree($items);

        // 缓存结果
        $cachedNavbar = $result;
        $cacheTime = time();

        return $result;
    } catch (Exception $e) {
        // 如果数据库出错，返回默认导航
        error_log("导航栏数据加载失败: " . $e->getMessage());
        return getDefaultNavbar();
    }
}

function buildNavbarTree($items) {
    $tree = [];
    $itemsById = [];
    
    // 按ID索引所有项目
    foreach ($items as $item) {
        $itemsById[$item['id']] = $item;
        $itemsById[$item['id']]['children'] = [];
    }
    
    // 构建树形结构
    foreach ($items as $item) {
        if ($item['parent_id'] === null) {
            $tree[] = &$itemsById[$item['id']];
        } else {
            if (isset($itemsById[$item['parent_id']])) {
                $itemsById[$item['parent_id']]['children'][] = &$itemsById[$item['id']];
            }
        }
    }
    
    return $tree;
}

function getDefaultNavbar() {
    return [
        [
            'id' => 1,
            'name' => '首页',
            'url' => '/index.php',
            'type' => 'link',
            'children' => []
        ],
        [
            'id' => 2,
            'name' => '组织',
            'url' => '#',
            'type' => 'dropdown',
            'children' => [
                ['id' => 21, 'name' => '关于我们', 'url' => '/about.php', 'type' => 'submenu'],
                ['id' => 22, 'name' => '团队介绍', 'url' => '/team.php', 'type' => 'submenu'],
                ['id' => 23, 'name' => '联系我们', 'url' => '/contact.php', 'type' => 'submenu']
            ]
        ],
        [
            'id' => 3,
            'name' => '服务',
            'url' => '/services.php',
            'type' => 'link',
            'children' => []
        ],
        [
            'id' => 4,
            'name' => '新闻',
            'url' => '/news.php',
            'type' => 'link',
            'children' => []
        ]
    ];
}

function renderNavbar($navbarData = null) {
    if ($navbarData === null) {
        $navbarData = getNavbarData();
    }
    
    $html = '';
    
    foreach ($navbarData as $item) {
        $html .= renderNavItem($item);
    }
    
    return $html;
}

function renderNavItem($item) {
    $hasChildren = !empty($item['children']);
    $itemClass = $hasChildren ? 'nav-item dropdown' : 'nav-item';
    
    $html = '<li class="' . $itemClass . '">';
    
    if ($hasChildren) {
        // 下拉菜单
        $html .= '<a href="' . htmlspecialchars($item['url']) . '" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">';
        $html .= htmlspecialchars($item['name']);
        $html .= '<svg class="dropdown-arrow" width="12" height="12" viewBox="0 0 24 24" fill="none">';
        $html .= '<path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2"/>';
        $html .= '</svg>';
        $html .= '</a>';
        
        // 下拉菜单内容
        $html .= '<ul class="dropdown-menu">';
        foreach ($item['children'] as $child) {
            $html .= '<li>';
            $html .= '<a href="' . htmlspecialchars($child['url']) . '" class="dropdown-item">';
            $html .= htmlspecialchars($child['name']);
            $html .= '</a>';
            $html .= '</li>';
        }
        $html .= '</ul>';
    } else {
        // 普通链接
        $html .= '<a href="' . htmlspecialchars($item['url']) . '" class="nav-link">';
        $html .= htmlspecialchars($item['name']);
        $html .= '</a>';
    }
    
    $html .= '</li>';
    
    return $html;
}

// 获取导航栏数据
$navbarData = getNavbarData();
?>

<!-- 导航栏HTML结构 -->
<nav class="navbar navbar-expand-lg">
    <div class="container-fluid">
        <!-- 品牌Logo -->
        <a class="navbar-brand" href="/index.php">
            <img src="/assets/images/logo.png" alt="比特熊智慧系统" class="navbar-logo">
            <span class="brand-text">比特熊智慧系统</span>
        </a>
        
        <!-- 移动端切换按钮 -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <!-- 导航菜单 -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <?php echo renderNavbar($navbarData); ?>
            </ul>
            
            <!-- 右侧操作区 -->
            <div class="navbar-actions">
                <div class="search-box">
                    <input type="text" placeholder="搜索..." class="search-input">
                    <button class="search-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                            <path d="M21 21L16.65 16.65" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </button>
                </div>
                
                <div class="user-menu">
                    <button class="user-btn">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M20 21V19A4 4 0 0 0 16 15H8A4 4 0 0 0 4 19V21" stroke="currentColor" stroke-width="2"/>
                            <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        用户中心
                    </button>
                </div>
                
                <button class="login-btn">
                    登录
                </button>
            </div>
        </div>
    </div>
</nav>

<style>
/* 导航栏样式 */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0.75rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    color: #1e293b;
    font-weight: 700;
    font-size: 1.25rem;
}

.navbar-logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
}

.brand-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-nav {
    display: flex;
    gap: 1rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    color: #64748b;
    text-decoration: none;
    font-weight: 500;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link:hover {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.dropdown-arrow {
    transition: transform 0.3s ease;
}

.dropdown:hover .dropdown-arrow {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
    min-width: 200px;
    list-style: none;
    margin: 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    color: #64748b;
    text-decoration: none;
    padding: 0.75rem 1rem;
    display: block;
    transition: all 0.2s ease;
    border-radius: 8px;
    margin: 0 0.5rem;
}

.dropdown-item:hover {
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
}

.navbar-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    padding: 0.5rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    outline: none;
    transition: all 0.3s ease;
    width: 200px;
}

.search-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-btn {
    position: absolute;
    right: 0.5rem;
    background: none;
    border: none;
    color: #64748b;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.search-btn:hover {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.user-btn, .login-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    color: #64748b;
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.login-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    color: white;
    border: none;
}

.user-btn:hover, .login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .navbar-nav {
        flex-direction: column;
        gap: 0;
    }
    
    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        background: #f8fafc;
        margin-left: 1rem;
    }
    
    .search-input {
        width: 150px;
    }
    
    .navbar-actions {
        flex-direction: column;
        gap: 0.5rem;
        margin-top: 1rem;
    }
}
</style>
